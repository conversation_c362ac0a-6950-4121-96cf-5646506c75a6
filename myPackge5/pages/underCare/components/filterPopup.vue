<template>
    <view>
        <u-popup v-model="showPicker" mode="bottom" border-radius="14" :closeable="true" @close="canel">
            <scroll-view scroll-y="true" class="scroll-view">
                <u-form :model="form" ref="form">
                    <!-- 库存统计 -->
                    <template v-if="filterType === 'inventory'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">品种：</h3>
                            <u-form-item prop="varietiesId">
                                <view class="select-input" @click="handleVarietiesClick">
                                    <text :style="form.varietiesName ? 'color:#222' : 'color:#999'">
                                        {{ form.varietiesName || '请选择品种' }}
                                    </text>
                                    <text class="arrow">></text>
                                </view>
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">圈舍：</h3>
                            <u-form-item prop="penId">
                                <view class="select-input" @click="handlePenClick">
                                    <text :style="form.penName ? 'color:#222' : 'color:#999'">
                                        {{ form.penName || '请选择圈舍' }}
                                    </text>
                                    <text class="arrow">></text>
                                </view>
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">栏位：</h3>
                            <u-form-item prop="fenceCode">
                                <view class="select-input" @click="handleFenceClick">
                                    <text :style="form.fenceCode ? 'color:#222' : 'color:#999'">
                                        {{ form.fenceCode || '请选择栏位' }}
                                    </text>
                                    <text class="arrow">></text>
                                </view>
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">体重范围：</h3>
                            <view class="weight-range">
                                <u-form-item prop="startWeight" style="flex: 1; margin-right: 10rpx;">
                                    <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                        v-model="form.startWeight" placeholder="开始重量" type="number" />
                                </u-form-item>
                                <text style="margin: 0 10rpx; line-height: 80rpx;">-</text>
                                <u-form-item prop="endWeight" style="flex: 1; margin-left: 10rpx;">
                                    <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                        v-model="form.endWeight" placeholder="结束重量" type="number" />
                                </u-form-item>
                            </view>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">批次：</h3>
                            <u-form-item prop="batch">
                                <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                    v-model="form.batch" placeholder="请输入批次" />
                            </u-form-item>
                        </view>
                    </template>

                    <!-- 饲养管理 -->
                    <template v-if="filterType === 'breeding'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">投喂圈舍：</h3>
                            <u-form-item prop="feedingArea">
                                <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                    v-model="form.feedingArea" placeholder="请输入养殖场名称，10个字以内" />
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">饲料种类：</h3>
                            <u-form-item prop="feedType">
                                <view class="select-input" @click="handleFeedTypeClick">
                                    <text :style="form.feedType ? 'color:#222' : 'color:#999'">
                                        {{ form.feedType || '请选择' }}
                                    </text>
                                    <text class="arrow">></text>
                                </view>
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">投喂时间：</h3>
                            <view class="time-view">
                                <view class="start-time"
                                    :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('startTime')">
                                    {{ startTime.title }}
                                </view>
                                -
                                <view class="start-time" :style="endTime.title == '结束时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('endTime')">
                                    {{ endTime.title }}
                                </view>
                            </view>
                        </view>
                    </template>

                    <!-- 生长监测 -->
                    <template v-if="filterType === 'growth'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">耳标号：</h3>
                            <u-form-item prop="earTagNo">
                                <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                    v-model="form.earTagNo" placeholder="请输入" />
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">检测时间：</h3>
                            <view class="time-view">
                                <view class="start-time"
                                    :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('startTime')">
                                    {{ startTime.title }}
                                </view>
                                -
                                <view class="start-time" :style="endTime.title == '结束时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('endTime')">
                                    {{ endTime.title }}
                                </view>
                            </view>
                        </view>
                    </template>

                    <!-- 接种记录 -->
                    <template v-if="filterType === 'vaccination'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">接种时间：</h3>
                            <view class="time-view">
                                <view class="start-time"
                                    :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('startTime')">
                                    {{ startTime.title }}
                                </view>
                                <view class="end-time"
                                    :style="endTime.title === '结束时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('endTime')">
                                    {{ endTime.title }}
                                </view>
                            </view>
                        </view>
                    </template>

                    <!-- 疾病防控 -->
                    <template v-if="filterType === 'disease'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">耳标号：</h3>
                            <u-form-item prop="earTagNo">
                                <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                    v-model="form.earTagNo" placeholder="请输入" />
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">检测时间：</h3>
                            <view class="time-view">
                                <view class="start-time"
                                    :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('startTime')">
                                    {{ startTime.title }}
                                </view>
                                -
                                <view class="start-time" :style="endTime.title == '结束时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('endTime')">
                                    {{ endTime.title }}
                                </view>
                            </view>
                        </view>
                    </template>

                    <!-- 日常记录 -->
                    <template v-if="filterType === 'daily'">
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">耳标号：</h3>
                            <u-form-item prop="earTagNo">
                                <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                    v-model="form.earTagNo" placeholder="请输入" />
                            </u-form-item>
                        </view>
                        <view class="regulatory-area">
                            <h3 class="regulatory-area-title">记录时间：</h3>
                            <view class="time-view">
                                <view class="start-time"
                                    :style="startTime.title === '开始时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('startTime')">
                                    {{ startTime.title }}
                                </view>
                                -
                                <view class="start-time" :style="endTime.title == '结束时间' ? 'color:#999' : 'color:#222'"
                                    @click="handleShowTime('endTime')">
                                    {{ endTime.title }}
                                </view>
                            </view>
                        </view>
                    </template>

                </u-form>
                <view :style="'height:' + (isIphonex ? 48 : 24) + 'rpx'"></view>
            </scroll-view>
            <view class="button-group">
                <view class="button-group-view box">
                    <view class="button-group-reset flex" @click="resetForm">
                        重置
                    </view>
                    <view class="button-group-submit flex" @click="submitForm">
                        确认
                    </view>
                </view>
            </view>
        </u-popup>

        <!-- 饲料种类选择器 -->
        <u-popup v-model="showFeedTypePicker" mode="bottom" border-radius="14">
            <view class="popup-view">
                <view class="picker-btn">
                    <view class="left" @click="showFeedTypePicker = false">取消</view>
                    <view class="middle">选择饲料种类</view>
                    <view class="right" @click="confirmFeedType">确定</view>
                </view>
                <picker-view :value="feedTypePickerValue" @change="onFeedTypePickerChange">
                    <picker-view-column>
                        <view class="item" v-for="(item, index) in feedTypeOptions" :key="index">{{ item }}</view>
                    </picker-view-column>
                </picker-view>
            </view>
        </u-popup>

        <!-- 品种选择器 -->
        <u-popup v-model="showVarietiesPicker" mode="bottom" border-radius="14">
            <view class="popup-view">
                <view class="picker-btn">
                    <view class="left" @click="showVarietiesPicker = false">取消</view>
                    <view class="middle">选择品种</view>
                    <view class="right" @click="confirmVarieties">确定</view>
                </view>
                <picker-view :value="varietiesPickerValue" @change="onVarietiesPickerChange">
                    <picker-view-column>
                        <view class="item" v-for="(item, index) in varietiesOptions" :key="index">{{ item.varietiesName }}</view>
                    </picker-view-column>
                </picker-view>
            </view>
        </u-popup>

        <!-- 圈舍选择器 -->
        <u-popup v-model="showPenPicker" mode="bottom" border-radius="14">
            <view class="popup-view">
                <view class="picker-btn">
                    <view class="left" @click="showPenPicker = false">取消</view>
                    <view class="middle">选择圈舍</view>
                    <view class="right" @click="confirmPen">确定</view>
                </view>
                <picker-view :value="penPickerValue" @change="onPenPickerChange">
                    <picker-view-column>
                        <view class="item" v-for="(item, index) in penOptions" :key="index">{{ item.penName }}</view>
                    </picker-view-column>
                </picker-view>
            </view>
        </u-popup>

        <!-- 栏位选择器 -->
        <u-popup v-model="showFencePicker" mode="bottom" border-radius="14">
            <view class="popup-view">
                <view class="picker-btn">
                    <view class="left" @click="showFencePicker = false">取消</view>
                    <view class="middle">选择栏位</view>
                    <view class="right" @click="confirmFence">确定</view>
                </view>
                <picker-view :value="fencePickerValue" @change="onFencePickerChange">
                    <picker-view-column>
                        <view class="item" v-for="(item, index) in fenceOptions" :key="index">{{ item.fenceCode }}</view>
                    </picker-view-column>
                </picker-view>
            </view>
        </u-popup>

        <u-calendar v-if="!showFeedTypePicker" range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F"
            range-color="#40CA8F" btn-type="success" v-model="showData" :mode="`range`" @change='changeData'>
        </u-calendar>
    </view>
</template>

<script>
import { getDicts } from '@/api/dict.js'
import { livestockVarietiesList, penList, fenceList } from '@/api/pages/livestock/farm'

export default {
    props: {
        pickerFilterShow: {
            type: Boolean,
            default: false
        },
        filterType: {
            type: String,
            default: 'inventory', // inventory, breeding, growth, vaccination, disease, daily
            validator: function (value) {
                return ['inventory', 'breeding', 'growth', 'vaccination', 'disease', 'daily'].indexOf(value) !== -1
            }
        },
        pastureId: {
            type: String,
            default: ''
        }
    },
    created() {
        this.loadFeedTypeDict()
        this.loadVarietiesList()
        if (this.pastureId) {
            this.loadPenList()
        }
    },
    data() {
        return {
            customStyle: { fontSize: '26rpx' },
            placeholderStyle: ';color:#999;font-size: 26rpx;',
            form: {
                // 通用字段
                earTagNo: "", // 耳标号
                startTime: "", // 开始时间
                endTime: "", // 结束时间
                // 库存统计专用字段
                varietiesId: "", // 品种ID
                varietiesName: "", // 品种名称
                penId: "", // 圈舍ID
                penName: "", // 圈舍名称
                fenceCode: "", // 栏位编号
                startWeight: "", // 开始重量
                endWeight: "", // 结束重量
                batch: "", // 批次
                // 日常记录专用字段
                feedingArea: "", // 投喂圈舍
                feedType: "", // 饲料种类
                feedTypeValue: "", // 饲料种类值
            },
            showPicker: false,
            showData: false,
            showFeedTypePicker: false,
            showVarietiesPicker: false,
            showPenPicker: false,
            showFencePicker: false,
            feedTypeOptions: [], // 饲料种类
            feedTypeDict: {}, // 饲料种类字典
            feedTypePickerValue: [0],
            tempFeedTypeIndex: 0,
            varietiesOptions: [], // 品种
            varietiesPickerValue: [0],
            tempVarietiesIndex: 0,
            penOptions: [], // 圈舍选项
            penPickerValue: [0], 
            tempPenIndex: 0, 
            fenceOptions: [], // 栏位选项
            fencePickerValue: [0],
            tempFenceIndex: 0, 
            currentTimeType: '', 
            startTime: {
                title: '开始时间',
                timestamp: 0
            },
            endTime: {
                title: '结束时间',
                timestamp: 0
            },
        }
    },
    watch: {
        pickerFilterShow: {
            handler(newValue) {
                this.showPicker = newValue
            },
            immediate: true,
            deep: true
        },
        filterType: {
            handler(newValue, oldValue) {
                // 切换tab时清空表单数据
                if (oldValue && newValue !== oldValue) {
                    this.reset()
                }
            }
        },
        pastureId: {
            handler(newValue) {
                if (newValue) {
                    this.loadPenList()
                } else {
                    this.penOptions = []
                    this.fenceOptions = []
                }
            },
            immediate: true
        }
    },
    computed: {
    },
    methods: {
        // 加载饲料种类
        async loadFeedTypeDict() {

            try {
                const res = await getDicts('pasture_feed_food');
                this.feedTypeOptions = res.data.map(item => item.dictLabel);
                this.feedTypeDict = res.data.reduce((dict, item) => {
                    dict[item.dictLabel] = item.dictValue;
                    return dict;
                }, {});
            } catch (error) {
                console.error('加载饲料种类字典失败:', error);
            }
        },

        // 加载品种列表
        async loadVarietiesList() {
            try {
                const res = await livestockVarietiesList({
                    pageNum: 1,
                    pageSize: 100
                });
                if (res?.code === 200 && res.result) {
                    this.varietiesOptions = res.result
                } else {
                    this.varietiesOptions = [];
                }
            } catch (error) {
                console.error('加载品种列表失败:', error);
                this.varietiesOptions = [];
            }
        },

        // 加载圈舍列表
        async loadPenList() {
            if (!this.pastureId) return;

            try {
                const res = await penList({
                    pageNum: 1,
                    pageSize: 999,
                    pastureId: this.pastureId
                });
                if (res?.code === 200 && res.result) {
                    this.penOptions = res.result;
                } else {
                    this.penOptions = [];
                }
            } catch (error) {
                console.error('加载圈舍列表失败:', error);
                this.penOptions = [];
            }
        },

        // 加载栏位列表
        async loadFenceList(penId) {
            if (!penId) return;

            try {
                const res = await fenceList({
                    pageNum: 1,
                    pageSize: 999,
                    pid: penId
                });
                if (res?.code === 200 && res.result?.list) {
                    this.fenceOptions = res.result.list;
                } else {
                    this.fenceOptions = [];
                }
            } catch (error) {
                console.error('加载栏位列表失败:', error);
                this.fenceOptions = [];
            }
        },

        handleFeedTypeClick() {
            this.showFeedTypePicker = true;
        },

        // 饲料种类选择器值变化
        onFeedTypePickerChange(e) {
            this.tempFeedTypeIndex = e.detail.value[0];
        },

        // 确认饲料种类选择
        confirmFeedType() {
            const selectedLabel = this.feedTypeOptions[this.tempFeedTypeIndex];
            Object.assign(this.form, {
                feedType: selectedLabel,
                feedTypeValue: this.feedTypeDict[selectedLabel] || selectedLabel
            });
            this.feedTypePickerValue = [this.tempFeedTypeIndex];
            this.showFeedTypePicker = false;
        },

        handleVarietiesClick() {
            this.showVarietiesPicker = true;
        },

        // 品种选择器值变化
        onVarietiesPickerChange(e) {
            this.tempVarietiesIndex = e.detail.value[0];
        },

        // 确认品种选择
        confirmVarieties() {
            const selectedVarieties = this.varietiesOptions[this.tempVarietiesIndex];
            if (selectedVarieties) {
                Object.assign(this.form, {
                    varietiesId: selectedVarieties.varietiesId,
                    varietiesName: selectedVarieties.varietiesName
                });
                this.varietiesPickerValue = [this.tempVarietiesIndex];
            }
            this.showVarietiesPicker = false;
        },

        handlePenClick() {
            if (this.penOptions.length === 0) {
                uni.showToast({
                    title: '暂无圈舍数据',
                    icon: 'none'
                });
                return;
            }
            this.showPenPicker = true;
        },

        // 圈舍选择器值变化
        onPenPickerChange(e) {
            this.tempPenIndex = e.detail.value[0];
        },

        // 确认圈舍选择
        confirmPen() {
            const selectedPen = this.penOptions[this.tempPenIndex];
            if (selectedPen) {
                Object.assign(this.form, {
                    penId: selectedPen.penId,
                    penName: selectedPen.penName,
                    // 清空栏位选择
                    fenceCode: ""
                });
                this.penPickerValue = [this.tempPenIndex];
                // 加载对应的栏位列表
                this.loadFenceList(selectedPen.penId);
            }
            this.showPenPicker = false;
        },

        handleFenceClick() {
            if (!this.form.penId) {
                uni.showToast({
                    title: '请先选择圈舍',
                    icon: 'none'
                });
                return;
            }
            if (this.fenceOptions.length === 0) {
                uni.showToast({
                    title: '该圈舍暂无栏位',
                    icon: 'none'
                });
                return;
            }
            this.showFencePicker = true;
        },

        // 栏位选择器值变化
        onFencePickerChange(e) {
            this.tempFenceIndex = e.detail.value[0];
        },

        // 确认栏位选择
        confirmFence() {
            const selectedFence = this.fenceOptions[this.tempFenceIndex];
            if (selectedFence) {
                Object.assign(this.form, {
                    fenceCode: selectedFence.fenceCode
                });
                this.fencePickerValue = [this.tempFenceIndex];
            }
            this.showFencePicker = false;
        },

        handleShowTime(type) {
            this.currentTimeType = type;
            this.showData = true;
        },
        changeData(e) {
            const { startDate, endDate } = e;
            Object.assign(this.startTime, { title: startDate });
            Object.assign(this.endTime, { title: endDate });
            Object.assign(this.form, { startTime: startDate, endTime: endDate });
            this.showData = false;
            this.currentTimeType = '';
        },

        canel() {
            // 取消时不清空表单数据，保持内容
            this.$emit('canel')
        },
        submitForm() {
            const filterData = {};
            const { form, filterType } = this;

            if (form.startTime) filterData.startTime = form.startTime;
            if (form.endTime) filterData.endTime = form.endTime;

            // 特定字段
            if (filterType === 'inventory') {
                // 库存统计筛选字段
                if (form.varietiesId) filterData.varietiesId = form.varietiesId;
                if (form.penId) filterData.penId = form.penId;
                if (form.fenceCode) filterData.fenceCode = form.fenceCode;
                if (form.startWeight) filterData.startWeight = form.startWeight;
                if (form.endWeight) filterData.endWeight = form.endWeight;
                if (form.batch) filterData.batch = form.batch;
            } else if (filterType === 'breeding') {
                if (form.feedingArea) filterData.pastureName = form.feedingArea;
                if (form.feedTypeValue) filterData.feedFood = form.feedTypeValue;
            } else if (filterType === 'vaccination') {
                // 接种记录只使用时间筛选
            } else {
                // growth、disease、daily 都使用耳标号
                if (form.earTagNo) filterData.earTagNo = form.earTagNo;
            }

            console.log('筛选数据:', filterData);
            this.$emit('submitForm', filterData);
        },
        reset() {
            Object.assign(this.startTime, { title: "开始时间", timestamp: 0 });
            Object.assign(this.endTime, { title: "结束时间", timestamp: 0 });

            Object.assign(this.form, {
                earTagNo: "",
                startTime: "",
                endTime: "",
                feedingArea: "",
                feedType: "",
                feedTypeValue: "",
                // 库存统计字段
                varietiesId: "",
                varietiesName: "",
                penId: "",
                penName: "",
                fenceCode: "",
                startWeight: "",
                endWeight: "",
                batch: ""
            });

            // 重置选择器
            this.feedTypePickerValue = [0];
            this.tempFeedTypeIndex = 0;
            this.varietiesPickerValue = [0];
            this.tempVarietiesIndex = 0;
            this.penPickerValue = [0];
            this.tempPenIndex = 0;
            this.fencePickerValue = [0];
            this.tempFenceIndex = 0;
            // 清空栏位选项
            this.fenceOptions = [];
        },
        resetForm() {
            this.reset()
            this.$emit('resetSearch', false)
            this.$emit('canel')
        }
    }
}
</script>

<style lang="scss" scoped>
.button-group {
    width: 100%;
    padding: 29rpx;
    position: relative;
    background-color: white;
    z-index: 10;
    // margin-top: 53rpx;

    .button-group-view {
        border-radius: 100rpx 100rpx 100rpx 100rpx;
        display: flex;
        background-color: #40CA8F;

        .button-group-reset {
            padding: 20rpx 0;
            text-align: center;
            background: #FFFFFF;
            border-radius: 100rpx 0rpx 120rpx 100rpx;
            border: 2rpx solid #40CA8F;
            font-size: 32rpx;
            color: #40CA8F;
            font-family: PingFang SC-Bold, PingFang SC;
        }

        .button-group-submit {
            padding: 20rpx 0;
            text-align: center;
            background: #40CA8F;
            font-size: 32rpx;
            border-radius: 0rpx 100rpx 100rpx 0rpx;
            color: #FFFFFF;
        }
    }

}

.regulatory-area {
    padding: 29rpx;

    /deep/ .u-form-item {
        padding: 0 !important;
    }

    .regulatory-area-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 400;
        padding-bottom: 24rpx;
        font-family: PingFang SC-Bold, PingFang SC;
    }

    .time-view {
        display: flex;
        justify-content: space-between;
        line-height: 50rpx;

        .start-time {
            width: 50%;
            border-bottom: 1px solid #F4F4F4;
            text-align: center;
            color: #999;
            padding-bottom: 20rpx;
        }
    }

    .select-input {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 26rpx;

        .arrow {
            color: #999;
            font-size: 24rpx;
        }
    }
}

.scroll-view {
    height: 1000rpx;
    overflow-y: scroll;
}

.job-other-type {
    padding: 8rpx 29rpx;
    // padding-top: 0;

    .job-type-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 400;
        padding-bottom: 24rpx;
        font-family: PingFang SC-Bold, PingFang SC;
    }

    .job-type-content {
        display: flex;
        flex-wrap: wrap;

        p {
            color: #999;
            background-color: #F4F4F4;
            padding: 17rpx 47rpx;
            border-radius: 100rpx 100rpx 100rpx 100rpx;
            margin: 0 21rpx 30rpx 0;
            border: 2rpx solid transparent;
        }

        .policy-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .ear-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }
    }
}

.job-type {
    padding: 29rpx;
    margin-top: 10rpx;

    .job-type-title {
        font-size: 28rpx;
        color: #333;
        font-weight: 400;
        padding-bottom: 24rpx;
        font-family: PingFang SC-Bold, PingFang SC;
    }

    .job-type-content {
        display: flex;
        flex-wrap: wrap;

        p {
            color: #999;
            background-color: #F4F4F4;
            padding: 17rpx 47rpx;
            border-radius: 100rpx 100rpx 100rpx 100rpx;
            margin: 0 30rpx 30rpx 0;
            border: 2rpx solid transparent;
        }

        .job-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .policy-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }

        .ear-type-active {
            border: 2rpx solid #40CA8F;
            color: #40CA8F;
            background-color: white;
        }
    }

}

uni-picker-view {
    display: block;
}

uni-picker-view .uni-picker-view-wrapper {
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    height: 100%;
    background-color: white;
}

uni-picker-view[hidden] {
    display: none;
}

picker-view {
    width: 100%;
    height: 600rpx;
    margin-top: 20rpx;
}

.item {
    display: flex;
    align-items: center;
    justify-content: center;
}

.popup-view {
    .picker-btn {
        display: flex;
        justify-content: space-between;
        padding: 30rpx 40rpx;

        .left {
            color: #999;
            font-size: 28rpx;
            font-family: PingFang SC-Medium;
        }

        .middle {
            font-size: 32rpx;
            color: #222;
            font-family: PingFang SC-Heavy;
        }

        .right {
            color: #40CA8F;
            font-size: 32rpx;
            font-family: PingFang SC-Medium;
        }
    }
}

// 库存统计专用样式
.weight-range {
    display: flex;
    align-items: center;
    gap: 10rpx;
}

.time-range {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}
</style>